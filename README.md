# Vietnamese Audiobook Text-to-Speech Processor

A modular Python application that processes Vietnamese audiobook chapters, adding pause tags using Gemini AI and synthesizing speech using Google Cloud Text-to-Speech with the Chirp 3 voice engine.

## Features

- **Modular Architecture**: Clean separation of concerns with dedicated modules for different functionalities
- **AI-Powered Text Processing**: Uses Gemini 2.5 Pro to intelligently add pause and break tags
- **High-Quality TTS**: Leverages Google Cloud TTS with Vietnamese Chirp 3 HD voice
- **Parallel Processing**: Concurrent synthesis of sentences and segments for faster processing
- **Multiple TTS Engines**: Support for Google Cloud TTS, OpenAI TTS, and ElevenLabs
- **Configurable Parameters**: Customizable break durations, worker counts, and batch sizes
- **Comprehensive CLI**: Rich command-line interface with validation and preview options

## Setup

### 1. Install Python
Make sure you have Python 3.8 or higher installed.

### 2. Install Dependencies
Install the required Python libraries:
```bash
pip install -r requirements.txt
```

### 3. Configure API Key
Copy the example configuration file and add your API keys:
```bash
cp config.example.ini config.ini
# Edit config.ini with your actual API keys
```

### 4. Google Cloud Authentication
This project uses the Google Cloud Text-to-Speech API. Set up authentication:

**Enable the API**: Make sure the Text-to-Speech API is enabled in your Google Cloud project.

**Set up credentials**:
```bash
gcloud auth application-default login
```
This will open a browser window for you to log in to your Google account. After logging in, your credentials will be stored locally.

## Project Structure

```
├── main.py                     # Main entry point
├── config/
│   ├── __init__.py
│   ├── settings.py             # Configuration management
│   └── config.ini              # Configuration file
├── cli/
│   ├── __init__.py
│   └── argument_parser.py      # Command-line interface
├── services/
│   ├── __init__.py
│   ├── file_service.py         # File I/O operations
│   ├── gemini_service.py       # Gemini AI integration
│   └── tts_service.py          # Google Cloud TTS integration
├── core/
│   ├── __init__.py
│   ├── text_processor.py       # Text processing orchestration
│   ├── segment_processor.py    # Segment and sentence processing
│   └── audio_synthesizer.py    # Audio synthesis orchestration
├── utils/
│   ├── __init__.py
│   ├── text_utils.py           # Text manipulation utilities
│   ├── audio_utils.py          # Audio processing utilities
│   └── concurrency_utils.py    # Threading and batching utilities
├── requirements.txt
└── README.md
```

## Usage

### Basic Usage
```bash
python main.py [input_file] [tagged_file] [output_audio_file]
```

### Advanced Usage
```bash
python main.py input.txt tagged.txt output.wav \
  --force-tagging \
  --max-workers 20 \
  --segment-batch-size 10 \
  --min-break 1000 \
  --max-break 2500 \
  --verbose
```

### Command-Line Options

#### Required Arguments
- `input_file`: Path to the input text file
- `tagged_file`: Path to the output tagged text file
- `output_file`: Path to the output audio file (e.g., chapter.wav)

#### Optional Flags
- `--force-tagging`: Force re-tagging even if tagged file exists
- `--preview`: Preview synthesis without generating audio
- `--validate-only`: Only validate input text without processing
- `--verbose`: Enable detailed output

#### Audio Processing
- `--min-break`: Minimum break duration in milliseconds (default: 1500)
- `--max-break`: Maximum break duration in milliseconds (default: 2000)

#### Performance
- `--max-workers`: Maximum parallel workers for sentence synthesis (default: 10)
- `--segment-batch-size`: Segments to process in parallel (default: 5)

#### Configuration
- `--config`: Path to configuration file (default: config.ini)

### Examples

#### Basic processing:
```bash
python main.py first_chapter_cleaned_v2.txt first_chapter_tagged.txt first_chapter.wav
```

#### Force re-tagging with custom parameters:
```bash
python main.py input.txt tagged.txt output.wav \
  --force-tagging \
  --max-workers 15 \
  --min-break 1200 \
  --max-break 2200
```

#### Preview without processing:
```bash
python main.py input.txt tagged.txt output.wav --preview
```

#### Validate input only:
```bash
python main.py input.txt tagged.txt output.wav --validate-only
```

## Configuration

The application can be configured through the `config.ini` file:

```ini
[gemini]
api_key = your_api_key_here

[tts]
language_code = vi-VN
voice_name = vi-VN-Chirp3-HD-Algenib
speaking_rate = 0.99
audio_encoding = LINEAR16

[processing]
min_break_duration = 1500
max_break_duration = 2000
max_workers = 10
segment_batch_size = 5
retry_attempts = 5
backoff_seconds = 15
```

## Architecture

### Modular Design
The application follows a clean modular architecture:

- **Services Layer**: Handles external API integrations (Gemini, Google Cloud TTS, File I/O)
- **Core Layer**: Contains business logic for text processing and audio synthesis
- **Utils Layer**: Provides reusable utilities for text, audio, and concurrency operations
- **CLI Layer**: Manages command-line interface and argument parsing
- **Config Layer**: Handles configuration management

### Key Benefits
- **Maintainability**: Each module has a single responsibility
- **Testability**: Components can be tested in isolation
- **Extensibility**: Easy to add new features or swap implementations
- **Reusability**: Modules can be reused across different projects

## Text Processing

The application uses Gemini 2.5 Pro to intelligently process Vietnamese text:

1. **Number Conversion**: "29.000 đô-la" → "hai mươi chín nghìn đô-la"
2. **Unit Conversion**: "6 kg" → "sáu ký-lô-gam"
3. **Name Phonetics**: "Phineas" → "Finias"
4. **Pause Insertion**: Strategic placement of `[pause]`, `[pause short]`, `[pause long]`, and `[break]` tags
5. **Text Normalization**: Proper capitalization and punctuation handling

## Audio Synthesis

The synthesis process includes:

1. **Parallel Processing**: Sentences within segments are synthesized concurrently
2. **Batch Processing**: Segments are processed in configurable batches
3. **Variable Breaks**: Random break durations between segments for natural flow
4. **Error Handling**: Automatic retry with exponential backoff for rate limits
5. **Progress Tracking**: Real-time progress reporting during synthesis

## Troubleshooting

### Common Issues

1. **API Key Error**: Ensure your Gemini API key is correctly set in `config.ini`
2. **Authentication Error**: Run `gcloud auth application-default login`
3. **Rate Limiting**: The application automatically handles rate limits with exponential backoff
4. **Memory Issues**: Reduce `max_workers` and `segment_batch_size` for large files

### Getting Help

Use the `--help` flag to see all available options:
```bash
python main.py --help
```

Use `--validate-only` to check your input before processing:
```bash
python main.py input.txt tagged.txt output.wav --validate-only
```

## Integration with Other Projects

For using this as a library in other Python projects:

### Method 1: Clone and Import
```bash
# Clone the repository
git clone https://bitbucket.org/fonos/google-voice.git

# In your Python code
import sys
sys.path.append('path/to/google-voice')
from api import process_text_to_audio
```

### Method 2: Git Submodule
```bash
# Add as submodule to your project
git submodule add https://bitbucket.org/fonos/google-voice.git libs/google-voice

# In your Python code
import sys
sys.path.append('libs/google-voice')
from api import process_text_to_audio
```

### Usage Examples
```python
# Simple usage
text = "Xin chào, đây là ví dụ về chuyển đổi văn bản thành giọng nói tiếng Việt."
audio_bytes = process_text_to_audio(text, return_bytes=True)

# Save to file
with open('output.wav', 'wb') as f:
    f.write(audio_bytes)

# With custom configuration
config = {
    "gemini_api_key": "your_api_key_here",
    "min_break": 1500,
    "max_break": 2000,
    "max_workers": 10
}

audio_bytes = process_text_to_audio(text, config=config, return_bytes=True)
```

## Requirements

- **Python**: 3.8 or higher
- **Dependencies**: See `requirements.txt`
- **APIs**: Gemini API key and Google Cloud authentication

## License

This project is provided as-is for educational and personal use.