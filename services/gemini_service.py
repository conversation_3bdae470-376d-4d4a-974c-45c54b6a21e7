"""
Gemini API service for text processing in Vietnamese audiobook TTS.
"""
import sys
import google.generativeai as genai
from typing import Optional


class GeminiService:
    """Service for interacting with the Gemini API with configurable prompts."""
    
    def __init__(self, api_key: str):
        """
        Initialize the Gemini service.
        
        Args:
            api_key: Gemini API key
        """
        self.api_key = api_key
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel('gemini-2.5-pro')
    
    def generate_tagged_text(self, text: str, prompt_template=None) -> str:
        """
        Use the Gemini API to generate text with pause tags using a configurable prompt.
        
        Args:
            text: Input text to process
            prompt_template: Optional prompt template to use. If None, uses default.
            
        Returns:
            Text with pause and break tags inserted
            
        Raises:
            SystemExit: If API call fails
        """
        if prompt_template:
            prompt = prompt_template.get_tagging_prompt(text)
        else:
            prompt = self._create_default_tagging_prompt(text)
        
        print("Generating tagged text with <PERSON>... This may take a moment.")
        try:
            response = self.model.generate_content(prompt)
            return response.text
        except Exception as e:
            print(f"An error occurred while calling the Gemini API: {e}")
            sys.exit(1)
    
    def _create_default_tagging_prompt(self, text: str) -> str:
        """
        Create the default prompt for text tagging (backward compatibility).
        
        Args:
            text: Input text to process
            
        Returns:
            Formatted prompt for Gemini API
        """
        return f"""
Please analyze the following Vietnamese text and insert pause and break tags to create natural-sounding pauses for a text-to-speech (TTS) engine.

Instructions:
1.  Use `[break]` for significant pauses between paragraphs. This is the most important instruction.
2.  Place pause tags immediately after the punctuation mark, not before.
3.  Use `[pause long]` for long pauses within a paragraph.
4.  Use `[pause]` for standard sentence breaks. When a hyphen connects two parts of a sentence, replace it with a `[pause]` tag.
5.  Use `[pause short]` for brief pauses to improve prosody.
6.  Convert all numbers to their Vietnamese text representation. For example, "29.000 đô-la" should be converted to "hai mươi chín nghìn đô-la".
7.  Convert all metric units to their full Vietnamese representation. For example, "6 kg" should be converted to "sáu ký-lô-gam", "10 cm" to "mười cen-ti-mét", and "5m" to "năm mét".
8.  Convert English names to a TTS-friendly phonetic representation. For example, "Phineas" should be converted to "Finias".
9.  Remove unnecessary punctuation within names, but preserve sentence-level punctuation. For example, "Bác sĩ J. M. Harlow" should become "Bác sĩ J M Harlow", but the period at the end of a sentence should be kept.
10. Remove special characters that should not be read aloud, such as quotation marks ("").
11. Convert all text to lowercase, except for proper nouns (e.g., names of people, places, companies).
12. Do not add any other XML or SSML tags.
13. Return only the modified text with the inserted tags. Do not include any explanations or introductory text in your response.
14. Crucially, do not alter, add, or remove any of the original text, other than the number to text and case conversions.
15. Strictly preserve the period (.) at the end of every sentence.

Here is the text to process:
---
{text}
"""
    
    def validate_api_key(self) -> bool:
        """
        Validate that the API key is properly configured.
        
        Returns:
            True if API key is valid, False otherwise
        """
        try:
            # Try a simple API call to validate the key
            test_response = self.model.generate_content("Test")
            return True
        except Exception:
            return False
    
    def get_model_info(self) -> Optional[dict]:
        """
        Get information about the current model.
        
        Returns:
            Model information dictionary or None if unavailable
        """
        try:
            # This would need to be implemented based on available Gemini API methods
            return {
                'model_name': 'gemini-2.5-pro',
                'api_key_configured': bool(self.api_key)
            }
        except Exception:
            return None