"""
File I/O service for Vietnamese audiobook TTS processing.
"""
import os
import sys
from typing import Optional


class FileService:
    """Service for handling file operations."""
    
    @staticmethod
    def read_text_file(file_path: str, encoding: str = 'utf-8') -> str:
        """
        Read the content of a text file.
        
        Args:
            file_path: Path to the file to read
            encoding: File encoding (default: utf-8)
            
        Returns:
            File content as string
            
        Raises:
            SystemExit: If file is not found
        """
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                return f.read()
        except FileNotFoundError:
            print(f"Error: Input file not found at {file_path}")
            sys.exit(1)
        except UnicodeDecodeError as e:
            print(f"Error: Unable to decode file {file_path} with encoding {encoding}: {e}")
            sys.exit(1)
    
    @staticmethod
    def write_text_file(file_path: str, content: str, encoding: str = 'utf-8') -> None:
        """
        Write content to a text file.
        
        Args:
            file_path: Path to the file to write
            content: Content to write
            encoding: File encoding (default: utf-8)
        """
        try:
            # Create directory if it doesn't exist
            dir_path = os.path.dirname(file_path)
            if dir_path:  # Only create directory if there's actually a directory path
                os.makedirs(dir_path, exist_ok=True)
            
            with open(file_path, 'w', encoding=encoding) as f:
                f.write(content)
        except IOError as e:
            print(f"Error: Unable to write to file {file_path}: {e}")
            sys.exit(1)
    
    @staticmethod
    def file_exists(file_path: str) -> bool:
        """
        Check if a file exists.
        
        Args:
            file_path: Path to check
            
        Returns:
            True if file exists, False otherwise
        """
        return os.path.exists(file_path)
    
    @staticmethod
    def get_file_size(file_path: str) -> Optional[int]:
        """
        Get the size of a file in bytes.
        
        Args:
            file_path: Path to the file
            
        Returns:
            File size in bytes, or None if file doesn't exist
        """
        try:
            return os.path.getsize(file_path)
        except OSError:
            return None
    
    @staticmethod
    def create_directory(dir_path: str) -> None:
        """
        Create a directory if it doesn't exist.
        
        Args:
            dir_path: Path to the directory to create
        """
        os.makedirs(dir_path, exist_ok=True)
    
    @staticmethod
    def get_file_extension(file_path: str) -> str:
        """
        Get the file extension from a file path.
        
        Args:
            file_path: Path to the file
            
        Returns:
            File extension (including the dot)
        """
        return os.path.splitext(file_path)[1]
    
    @staticmethod
    def validate_output_path(file_path: str) -> bool:
        """
        Validate that an output path is writable.
        
        Args:
            file_path: Path to validate
            
        Returns:
            True if path is writable, False otherwise
        """
        try:
            # Check if directory exists or can be created
            dir_path = os.path.dirname(file_path)
            if dir_path:
                os.makedirs(dir_path, exist_ok=True)
            
            # Try to create a temporary file to test write permissions
            test_file = file_path + '.tmp'
            with open(test_file, 'w') as f:
                f.write('test')
            os.remove(test_file)
            return True
        except (OSError, IOError):
            return False