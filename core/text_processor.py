"""
Text processing orchestration for Vietnamese audiobook TTS.
"""
from typing import Optional
from services import FileService, GeminiService
from utils import validate_text_input, clean_text_for_tts
from adapters.base import TTSAdapterInterface


class TextProcessor:
    """Orchestrates text processing operations with TTS adapter support."""
    
    def __init__(self, gemini_service: GeminiService, file_service: FileService,
                 tts_adapter: Optional[TTSAdapterInterface] = None):
        """
        Initialize the text processor.
        
        Args:
            gemini_service: Service for Gemini API operations
            file_service: Service for file operations
            tts_adapter: Optional TTS adapter for engine-specific processing
        """
        self.gemini_service = gemini_service
        self.file_service = file_service
        self.tts_adapter = tts_adapter
    
    def process_text_file(self, input_file: str, tagged_file: str,
                         force_tagging: bool = False) -> str:
        """
        Process a text file, generating tagged text if needed.
        
        Args:
            input_file: Path to the input text file
            tagged_file: Path to the tagged text file
            force_tagging: Whether to force re-tagging even if tagged file exists
            
        Returns:
            Tagged text content
        """
        # Check if tagged file exists and if we should use it
        if self.file_service.file_exists(tagged_file) and not force_tagging:
            print(f"Tagged file found at {tagged_file}. Skipping Gemini tagging.")
            tagged_text = self.file_service.read_text_file(tagged_file)
            
            # Validate tagged text for the current TTS engine
            if self.tts_adapter and not self.tts_adapter.get_prompt_template().validate_tagged_text(tagged_text):
                print(f"Warning: Tagged text may not be compatible with {self.tts_adapter.get_engine_name()}")
            
            return tagged_text
        
        # Generate tagged text
        if force_tagging:
            print("Forcing Gemini tagging as requested.")
        else:
            print(f"Tagged file not found. Reading input file: {input_file}")
        
        # Read and validate input text
        raw_text = self.file_service.read_text_file(input_file)
        if not validate_text_input(raw_text):
            raise ValueError(f"Input file {input_file} contains no valid text content")
        
        # Clean the text
        cleaned_text = clean_text_for_tts(raw_text)
        
        # Generate tagged text using Gemini with TTS-specific prompt
        if self.tts_adapter:
            prompt_template = self.tts_adapter.get_prompt_template()
            print(f"Using {self.tts_adapter.get_engine_name()} prompt template for tagging.")
            tagged_text = self.gemini_service.generate_tagged_text(cleaned_text, prompt_template)
        else:
            # Fallback to default prompt
            print("Using default prompt template for tagging.")
            tagged_text = self.gemini_service.generate_tagged_text(cleaned_text)
        
        # Save tagged text
        print(f"Saving tagged text to {tagged_file}...")
        self.file_service.write_text_file(tagged_file, tagged_text)
        
        return tagged_text
    
    def validate_tagged_text(self, text: str) -> bool:
        """
        Validate that tagged text contains proper formatting.
        
        Args:
            text: Tagged text to validate
            
        Returns:
            True if text appears to be properly tagged
        """
        if not validate_text_input(text):
            return False
        
        # Check for presence of break tags (most important)
        has_breaks = '[break]' in text
        
        # Check for other pause tags
        has_pauses = any(tag in text for tag in ['[pause]', '[pause short]', '[pause long]'])
        
        return has_breaks or has_pauses
    
    def get_text_statistics(self, text: str) -> dict:
        """
        Get statistics about the text content.
        
        Args:
            text: Text to analyze
            
        Returns:
            Dictionary containing text statistics
        """
        if not text:
            return {}
        
        # Count different types of tags
        break_count = text.count('[break]')
        pause_count = text.count('[pause]')
        pause_short_count = text.count('[pause short]')
        pause_long_count = text.count('[pause long]')
        
        # Count sentences (approximate)
        sentence_count = text.count('.') + text.count('!') + text.count('?')
        
        # Count words (approximate)
        word_count = len(text.split())
        
        # Count characters
        char_count = len(text)
        
        return {
            'character_count': char_count,
            'word_count': word_count,
            'sentence_count': sentence_count,
            'break_tags': break_count,
            'pause_tags': pause_count,
            'pause_short_tags': pause_short_count,
            'pause_long_tags': pause_long_count,
            'total_pause_tags': pause_count + pause_short_count + pause_long_count
        }
    
    def preview_text_segments(self, text: str, max_segments: int = 5) -> list:
        """
        Get a preview of text segments for verification.
        
        Args:
            text: Tagged text to preview
            max_segments: Maximum number of segments to return
            
        Returns:
            List of text segments (up to max_segments)
        """
        from utils import split_text_by_breaks
        
        segments = split_text_by_breaks(text)
        return segments[:max_segments]