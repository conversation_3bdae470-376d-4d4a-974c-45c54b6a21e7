"""
Segment and sentence processing for Vietnamese audiobook TTS.
"""
from typing import List, <PERSON><PERSON>, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
from pydub import AudioSegment
from adapters.base import TTSAdapterInterface
from utils import split_segment_by_punctuation, ProcessingLogger


class SegmentProcessor:
    """Processes text segments and sentences for TTS synthesis with adapter support."""
    
    def __init__(self, tts_adapter: TTSAdapterInterface,
                 logger: Optional[ProcessingLogger] = None):
        """
        Initialize the segment processor.
        
        Args:
            tts_adapter: TTS adapter for synthesis operations
            logger: Optional processing logger for saving individual sentence files
        """
        self.tts_adapter = tts_adapter
        self.logger = logger
    
    def process_segment(self, segment: str, max_workers: int = 10,
                       segment_index: int = None, global_sentence_start_index: int = None) -> AudioSegment:
        """
        Process a single text segment, synthesizing its sentences in parallel.
        
        Args:
            segment: The text segment to synthesize
            max_workers: Maximum number of parallel workers for sentence synthesis
            segment_index: Index of the segment for logging purposes
            global_sentence_start_index: Starting global index for sentences in this segment
            
        Returns:
            AudioSegment containing the synthesized audio for the segment
        """
        segment = segment.strip()
        if not segment:
            return AudioSegment.empty()
        
        sentences = split_segment_by_punctuation(segment)
        
        # Handle single sentence or empty segments
        if len(sentences) <= 1:
            audio_segment = self.tts_adapter.synthesize_to_audio_segment(segment)
            # Save individual sentence file if logger is available
            if self.logger and global_sentence_start_index is not None:
                global_index = global_sentence_start_index
                self.logger.add_sentence(segment, segment_index, 0, global_index)
                audio_file_path = self.logger.get_audio_file_path(global_index)
                audio_segment.export(audio_file_path, format="wav")
                # Update audio metrics after file is saved
                self.logger.update_sentence_audio_metrics(global_index, audio_file_path)
            return audio_segment
        
        # Process multiple sentences in parallel
        print(f"  Synthesizing {len(sentences)} sentences in parallel for a segment...")
        return self._process_sentences_parallel(sentences, max_workers, segment_index, global_sentence_start_index)
    
    def _process_sentences_parallel(self, sentences: List[str], max_workers: int,
                                   segment_index: int = None, global_sentence_start_index: int = None) -> AudioSegment:
        """
        Process multiple sentences in parallel.
        
        Args:
            sentences: List of sentences to process
            max_workers: Maximum number of parallel workers
            segment_index: Index of the segment for logging purposes
            global_sentence_start_index: Starting global index for sentences in this segment
            
        Returns:
            Combined audio segment
        """
        segment_audio = AudioSegment.empty()
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all sentence synthesis tasks
            future_to_index = {
                executor.submit(self.tts_adapter.synthesize_sentence, sentence, idx): idx
                for idx, sentence in enumerate(sentences)
            }
            
            # Collect results in order
            sentence_results = [None] * len(sentences)
            for future in as_completed(future_to_index):
                sentence_index_in_segment = future_to_index[future]
                try:
                    _, audio_segment = future.result()
                    sentence_results[sentence_index_in_segment] = audio_segment
                    
                    # Save individual sentence file if logger is available
                    if self.logger and global_sentence_start_index is not None:
                        sentence_text = sentences[sentence_index_in_segment]
                        global_index = global_sentence_start_index + sentence_index_in_segment
                        self.logger.add_sentence(
                            sentence_text, segment_index, sentence_index_in_segment, global_index
                        )
                        audio_file_path = self.logger.get_audio_file_path(global_index)
                        audio_segment.export(audio_file_path, format="wav")
                        # Update audio metrics after file is saved
                        self.logger.update_sentence_audio_metrics(global_index, audio_file_path)
                        
                except Exception as e:
                    print(f"Error processing sentence {sentence_index_in_segment + 1}: {e}")
                    raise e
            
            # Combine audio segments in order
            for audio_segment in sentence_results:
                if audio_segment is not None:
                    segment_audio += audio_segment
        
        return segment_audio
    
    def process_segments_batch(self, segments: List[str], max_workers: int = 10,
                              batch_start_index: int = 0, global_sentence_indices: List[int] = None) -> List[AudioSegment]:
        """
        Process a batch of segments in parallel.
        
        Args:
            segments: List of text segments to process
            max_workers: Maximum number of parallel workers
            batch_start_index: Starting index for segment numbering
            global_sentence_indices: List of starting global sentence indices for each segment
            
        Returns:
            List of audio segments in the same order as input
        """
        if not segments:
            return []
        
        print(f"Processing batch with {len(segments)} segments...")
        
        with ThreadPoolExecutor(max_workers=len(segments)) as executor:
            # Submit all segment processing tasks
            future_to_segment = {}
            for idx, segment in enumerate(segments):
                segment_index = batch_start_index + idx
                global_start_index = global_sentence_indices[idx] if global_sentence_indices else None
                future_to_segment[executor.submit(
                    self.process_segment, segment, max_workers, segment_index, global_start_index
                )] = (segment, idx)
            
            # Collect results maintaining order
            batch_results = {}
            for future in as_completed(future_to_segment):
                segment_text, segment_idx = future_to_segment[future]
                try:
                    audio_segment = future.result()
                    batch_results[segment_text] = audio_segment
                except Exception as e:
                    print(f"Error processing segment: {segment_text[:50]}...: {e}")
                    raise e
            
            # Return results in original order
            return [batch_results[segment] for segment in segments]
    
    def validate_segment(self, segment: str) -> bool:
        """
        Validate that a segment is suitable for processing.
        
        Args:
            segment: Text segment to validate
            
        Returns:
            True if segment is valid for processing
        """
        if not segment or not segment.strip():
            return False
        
        # Check for reasonable length (not too short or too long)
        stripped = segment.strip()
        if len(stripped) < 3:  # Too short
            return False
        
        if len(stripped) > 5000:  # Too long for single segment
            return False
        
        return True
    
    def get_segment_statistics(self, segment: str) -> dict:
        """
        Get statistics about a text segment.
        
        Args:
            segment: Text segment to analyze
            
        Returns:
            Dictionary containing segment statistics
        """
        if not segment:
            return {}
        
        sentences = split_segment_by_punctuation(segment)
        
        return {
            'character_count': len(segment),
            'word_count': len(segment.split()),
            'sentence_count': len(sentences),
            'estimated_synthesis_time': len(sentences) * 2,  # Rough estimate in seconds
            'has_pause_tags': any(tag in segment for tag in ['[pause]', '[pause short]', '[pause long]'])
        }
    
    def preview_sentences(self, segment: str) -> List[str]:
        """
        Preview the sentences that would be generated from a segment.
        
        Args:
            segment: Text segment to preview
            
        Returns:
            List of sentences
        """
        return split_segment_by_punctuation(segment)