"""
Logging utilities for audiobook processing with timestamped folders and sentence tracking.
"""
import os
import json
import re
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path
from pydub import AudioSegment


class ProcessingLogger:
    """Manages logging for audiobook processing with timestamped folders."""
    
    def __init__(self, base_log_dir: str = "logs"):
        """
        Initialize the processing logger.
        
        Args:
            base_log_dir: Base directory for all logs
        """
        self.base_log_dir = Path(base_log_dir)
        self.session_dir = None
        self.audio_dir = None
        self.sentences_file = None
        self.sentences_data = []
        self.sentence_counter = 0
        
    def create_session_folder(self) -> str:
        """
        Create a timestamped session folder for the current processing run.
        
        Returns:
            Path to the created session folder
        """
        # Create timestamp in format: YYYY-MM-DD_HH-MM-SS
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        
        # Create session directory
        self.session_dir = self.base_log_dir / timestamp
        self.session_dir.mkdir(parents=True, exist_ok=True)
        
        # Create audio subdirectory
        self.audio_dir = self.session_dir / "audio"
        self.audio_dir.mkdir(exist_ok=True)
        
        # Initialize sentences file path
        self.sentences_file = self.session_dir / "sentences.json"
        
        print(f"Created session folder: {self.session_dir}")
        return str(self.session_dir)
    
    def _count_words_excluding_tags(self, text: str) -> int:
        """
        Count words in text, excluding pause tags.
        
        Args:
            text: The sentence text
            
        Returns:
            Number of words excluding tags
        """
        # Remove pause tags like [pause], [pause short], [pause long]
        clean_text = re.sub(r'\[pause[^\]]*\]', '', text)
        # Split by whitespace and count non-empty words
        words = [word.strip() for word in clean_text.split() if word.strip()]
        return len(words)
    
    def _get_audio_duration(self, audio_file_path: str) -> Optional[float]:
        """
        Get the duration of an audio file in seconds.
        
        Args:
            audio_file_path: Path to the audio file
            
        Returns:
            Duration in seconds, or None if file doesn't exist or error
        """
        try:
            if os.path.exists(audio_file_path):
                audio = AudioSegment.from_wav(audio_file_path)
                return len(audio) / 1000.0  # Convert milliseconds to seconds
        except Exception as e:
            print(f"Warning: Could not get duration for {audio_file_path}: {e}")
        return None
    
    def _calculate_speak_rate(self, word_count: int, duration_seconds: float) -> Optional[float]:
        """
        Calculate speaking rate in words per minute.
        
        Args:
            word_count: Number of words
            duration_seconds: Audio duration in seconds
            
        Returns:
            Speaking rate in WPM, or None if duration is 0
        """
        if duration_seconds > 0:
            return (word_count / duration_seconds) * 60.0
        return None

    def add_sentence(self, sentence_text: str, segment_index: int = None,
                    sentence_index_in_segment: int = None, global_index: int = None) -> int:
        """
        Add a sentence to the tracking list.
        
        Args:
            sentence_text: The text of the sentence
            segment_index: Index of the segment this sentence belongs to
            sentence_index_in_segment: Index of sentence within its segment
            global_index: Pre-calculated global index to maintain order
            
        Returns:
            Global sentence index
        """
        if self.session_dir is None:
            raise RuntimeError("Session folder not created. Call create_session_folder() first.")
        
        # Use provided global_index or auto-increment
        if global_index is not None:
            actual_global_index = global_index
        else:
            actual_global_index = self.sentence_counter
            self.sentence_counter += 1
        
        sentence_data = {
            "global_index": actual_global_index,
            "text": sentence_text.strip(),
            "segment_index": segment_index,
            "sentence_index_in_segment": sentence_index_in_segment,
            "audio_filename": f"{actual_global_index:04d}.wav",
            "timestamp": datetime.now().isoformat(),
            "word_count": self._count_words_excluding_tags(sentence_text),
            "audio_duration_seconds": None,  # Will be updated after audio generation
            "speak_rate_wpm": None  # Will be calculated after audio generation
        }
        
        self.sentences_data.append(sentence_data)
        
        return actual_global_index
    
    def update_sentence_audio_metrics(self, global_index: int, audio_file_path: str) -> None:
        """
        Update audio duration and speaking rate for a sentence after audio generation.
        
        Args:
            global_index: Global sentence index
            audio_file_path: Path to the generated audio file
        """
        # Find the sentence data
        sentence_data = None
        for data in self.sentences_data:
            if data["global_index"] == global_index:
                sentence_data = data
                break
        
        if sentence_data is None:
            print(f"Warning: Could not find sentence data for global_index {global_index}")
            return
        
        # Get audio duration
        duration = self._get_audio_duration(audio_file_path)
        if duration is not None:
            sentence_data["audio_duration_seconds"] = round(duration, 3)
            
            # Calculate speaking rate
            word_count = sentence_data["word_count"]
            speak_rate = self._calculate_speak_rate(word_count, duration)
            if speak_rate is not None:
                sentence_data["speak_rate_wpm"] = round(speak_rate, 1)

    def get_audio_file_path(self, sentence_index: int) -> str:
        """
        Get the full path for a sentence audio file.
        
        Args:
            sentence_index: Global sentence index
            
        Returns:
            Full path to the audio file
        """
        if self.audio_dir is None:
            raise RuntimeError("Session folder not created. Call create_session_folder() first.")
        
        filename = f"{sentence_index:04d}.wav"
        return str(self.audio_dir / filename)
    
    def save_sentences_list(self) -> None:
        """Save the sentences list to a JSON file, sorted by global_index."""
        if self.sentences_file is None:
            raise RuntimeError("Session folder not created. Call create_session_folder() first.")
        
        # Sort sentences by global_index to maintain original text order
        sorted_sentences = sorted(self.sentences_data, key=lambda x: x["global_index"])
        
        # Create summary data
        summary = {
            "session_info": {
                "timestamp": datetime.now().isoformat(),
                "total_sentences": len(sorted_sentences),
                "total_segments": len(set(s["segment_index"] for s in sorted_sentences if s["segment_index"] is not None))
            },
            "sentences": sorted_sentences
        }
        
        with open(self.sentences_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        print(f"Saved sentences list to: {self.sentences_file}")
    
    def create_session_summary(self, processing_stats: Dict[str, Any] = None) -> None:
        """
        Create a summary file for the processing session.
        
        Args:
            processing_stats: Optional processing statistics to include
        """
        if self.session_dir is None:
            raise RuntimeError("Session folder not created. Call create_session_folder() first.")
        
        summary_file = self.session_dir / "session_summary.json"
        
        summary_data = {
            "session_info": {
                "start_time": datetime.now().isoformat(),
                "session_folder": str(self.session_dir),
                "audio_folder": str(self.audio_dir),
                "sentences_file": str(self.sentences_file)
            },
            "statistics": {
                "total_sentences": len(self.sentences_data),
                "total_segments": len(set(s["segment_index"] for s in self.sentences_data if s["segment_index"] is not None)),
                "audio_files_created": len([f for f in self.audio_dir.glob("*.wav")]) if self.audio_dir.exists() else 0
            }
        }
        
        if processing_stats:
            summary_data["processing_stats"] = processing_stats
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary_data, f, ensure_ascii=False, indent=2)
        
        print(f"Created session summary: {summary_file}")
    
    def get_session_info(self) -> Dict[str, str]:
        """
        Get information about the current session.
        
        Returns:
            Dictionary with session information
        """
        if self.session_dir is None:
            return {}
        
        return {
            "session_dir": str(self.session_dir),
            "audio_dir": str(self.audio_dir),
            "sentences_file": str(self.sentences_file),
            "total_sentences": len(self.sentences_data)
        }


def create_timestamped_log_folder(base_dir: str = "logs") -> ProcessingLogger:
    """
    Create a timestamped log folder and return a ProcessingLogger instance.
    
    Args:
        base_dir: Base directory for logs
        
    Returns:
        Configured ProcessingLogger instance
    """
    logger = ProcessingLogger(base_dir)
    logger.create_session_folder()
    return logger