"""
Text processing utilities for Vietnamese audiobook TTS.
"""
import re
from typing import List


def split_segment_by_punctuation(segment: str) -> List[str]:
    """
    Splits a text segment into sentences based on punctuation marks.
    Preserves pause tags within sentences.
    
    Args:
        segment: Text segment to split
        
    Returns:
        List of sentences with preserved formatting
    """
    # Split by major punctuation but preserve pause tags
    # Use regex to split on sentence-ending punctuation followed by whitespace
    sentences = re.split(r'([.!?]+)', segment)
    
    # Recombine punctuation with preceding text
    result = []
    for i in range(0, len(sentences) - 1, 2):
        if i + 1 < len(sentences):
            sentence = sentences[i] + sentences[i + 1]
            sentence = sentence.strip()
            if sentence:
                result.append(sentence)
        else:
            sentence = sentences[i].strip()
            if sentence:
                result.append(sentence)
    
    # Handle case where last element doesn't have punctuation
    if len(sentences) % 2 == 1 and sentences[-1].strip():
        result.append(sentences[-1].strip())
    
    return result


def split_text_by_breaks(text: str) -> List[str]:
    """
    Split text by [break] tags and return clean segments.
    
    Args:
        text: Text containing [break] tags
        
    Returns:
        List of text segments without [break] tags
    """
    return [seg.strip() for seg in text.split("[break]") if seg.strip()]


def validate_text_input(text: str) -> bool:
    """
    Validate that text input is not empty and contains meaningful content.
    
    Args:
        text: Text to validate
        
    Returns:
        True if text is valid, False otherwise
    """
    return bool(text and text.strip())


def clean_text_for_tts(text: str) -> str:
    """
    Clean text for TTS processing by removing unwanted characters.
    
    Args:
        text: Raw text to clean
        
    Returns:
        Cleaned text suitable for TTS
    """
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text)
    
    # Remove leading/trailing whitespace
    text = text.strip()
    
    return text