"""
Audio processing utilities for Vietnamese audiobook TTS.
"""
import random
from pydub import AudioSegment
from typing import List, <PERSON><PERSON>


def create_variable_break_pause(min_duration: int, max_duration: int) -> AudioSegment:
    """
    Create a variable-length silence for breaks between segments.
    
    Args:
        min_duration: Minimum break duration in milliseconds
        max_duration: Maximum break duration in milliseconds
        
    Returns:
        AudioSegment containing silence
    """
    duration = random.randint(min_duration, max_duration)
    return AudioSegment.silent(duration=duration)


def combine_audio_segments(segments: List[AudioSegment], 
                          min_break: int = 1500, 
                          max_break: int = 2000) -> AudioSegment:
    """
    Combine multiple audio segments with variable breaks between them.
    
    Args:
        segments: List of audio segments to combine
        min_break: Minimum break duration in milliseconds
        max_break: Maximum break duration in milliseconds
        
    Returns:
        Combined audio segment
    """
    if not segments:
        return AudioSegment.empty()
    
    combined_audio = AudioSegment.empty()
    
    for i, audio_segment in enumerate(segments):
        if audio_segment:
            combined_audio += audio_segment
            if i < len(segments) - 1:
                break_pause = create_variable_break_pause(min_break, max_break)
                combined_audio += break_pause
                print(f"Added {break_pause.duration_seconds:.2f}s break after segment {i+1}")
    
    return combined_audio


def export_audio(audio: AudioSegment, output_path: str, format: str = "wav") -> None:
    """
    Export audio segment to file.
    
    Args:
        audio: Audio segment to export
        output_path: Path to save the audio file
        format: Audio format (default: wav)
    """
    print(f"Exporting combined audio to {output_path}...")
    audio.export(output_path, format=format)
    print(f"Audio content written to file {output_path}")


def validate_audio_parameters(min_break: int, max_break: int, 
                            max_workers: int, segment_batch_size: int) -> None:
    """
    Validate audio processing parameters.
    
    Args:
        min_break: Minimum break duration in milliseconds
        max_break: Maximum break duration in milliseconds
        max_workers: Maximum number of parallel workers
        segment_batch_size: Number of segments to process in parallel
        
    Raises:
        ValueError: If parameters are invalid
    """
    if min_break < 0 or max_break < 0:
        raise ValueError("Break durations must be non-negative")
    if min_break >= max_break:
        raise ValueError("min_break_duration must be less than max_break_duration")
    if max_workers < 1:
        raise ValueError("max_workers must be at least 1")
    if segment_batch_size < 1:
        raise ValueError("segment_batch_size must be at least 1")


def get_audio_duration_info(audio: AudioSegment) -> Tuple[float, int]:
    """
    Get duration information for an audio segment.
    
    Args:
        audio: Audio segment to analyze
        
    Returns:
        Tuple of (duration_in_seconds, duration_in_milliseconds)
    """
    duration_ms = len(audio)
    duration_seconds = duration_ms / 1000.0
    return duration_seconds, duration_ms