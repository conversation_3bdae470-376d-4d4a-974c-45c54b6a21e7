"""
Abstract base classes and interfaces for TTS adapters.

This module defines the Strategy pattern interfaces that all TTS adapters
must implement to ensure consistent behavior across different TTS engines.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Tuple, List, Optional
from pydub import AudioSegment


class PromptTemplateInterface(ABC):
    """Interface for TTS-specific prompt templates."""
    
    @abstractmethod
    def get_tagging_prompt(self, text: str) -> str:
        """
        Generate a TTS-specific prompt for text tagging.
        
        Args:
            text: Input text to process
            
        Returns:
            Formatted prompt for the specific TTS engine
        """
        pass
    
    @abstractmethod
    def get_supported_tags(self) -> List[str]:
        """
        Get list of supported pause/break tags for this TTS engine.
        
        Returns:
            List of supported tag names
        """
        pass
    
    @abstractmethod
    def validate_tagged_text(self, text: str) -> bool:
        """
        Validate that tagged text contains proper formatting for this TTS engine.
        
        Args:
            text: Tagged text to validate
            
        Returns:
            True if text is properly formatted for this TTS engine
        """
        pass


class TTSAdapterInterface(ABC):
    """Interface for TTS engine adapters."""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the TTS adapter.
        
        Args:
            config: TTS engine-specific configuration
        """
        self.config = config
        self.prompt_template = self._create_prompt_template()
    
    @abstractmethod
    def _create_prompt_template(self) -> PromptTemplateInterface:
        """
        Create the prompt template for this TTS engine.
        
        Returns:
            Prompt template instance
        """
        pass
    
    @abstractmethod
    def synthesize_speech(self, text: str, retries: int = 5) -> bytes:
        """
        Synthesize speech from text.
        
        Args:
            text: Text to synthesize
            retries: Number of retry attempts
            
        Returns:
            Audio data as bytes
            
        Raises:
            Exception: If synthesis fails after all retries
        """
        pass
    
    @abstractmethod
    def synthesize_to_audio_segment(self, text: str) -> AudioSegment:
        """
        Synthesize text to AudioSegment.
        
        Args:
            text: Text to synthesize
            
        Returns:
            AudioSegment containing the synthesized audio
        """
        pass
    
    @abstractmethod
    def synthesize_sentence(self, sentence: str, sentence_index: int) -> Tuple[int, AudioSegment]:
        """
        Synthesize a single sentence to audio.
        
        Args:
            sentence: Sentence to synthesize
            sentence_index: Index of the sentence for ordering
            
        Returns:
            Tuple of (sentence_index, AudioSegment) for maintaining order
        """
        pass
    
    @abstractmethod
    def get_available_voices(self) -> List[str]:
        """
        Get list of available voices for the configured language.
        
        Returns:
            List of available voice names/IDs
        """
        pass
    
    @abstractmethod
    def validate_config(self) -> bool:
        """
        Validate that the adapter configuration is valid.
        
        Returns:
            True if configuration is valid, False otherwise
        """
        pass
    
    @abstractmethod
    def get_engine_name(self) -> str:
        """
        Get the name of this TTS engine.
        
        Returns:
            Engine name string
        """
        pass
    
    def get_prompt_template(self) -> PromptTemplateInterface:
        """
        Get the prompt template for this TTS engine.
        
        Returns:
            Prompt template instance
        """
        return self.prompt_template
    
    def get_service_info(self) -> Dict[str, Any]:
        """
        Get information about the TTS service configuration.
        
        Returns:
            Dictionary containing service information
        """
        return {
            'engine_name': self.get_engine_name(),
            'config': self.config,
            'supported_tags': self.prompt_template.get_supported_tags(),
            'config_valid': self.validate_config()
        }


class BaseTTSAdapter(TTSAdapterInterface):
    """
    Base implementation of TTSAdapterInterface with common functionality.
    
    This class provides default implementations for common methods
    that can be shared across different TTS adapters.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the base TTS adapter.
        
        Args:
            config: TTS engine-specific configuration
        """
        super().__init__(config)
        self._validate_required_config()
    
    def _validate_required_config(self) -> None:
        """
        Validate that required configuration keys are present.
        Override in subclasses to define required keys.
        """
        pass
    
    def _handle_synthesis_error(self, error: Exception, text: str, attempt: int, max_retries: int) -> None:
        """
        Handle synthesis errors with logging and retry logic.
        
        Args:
            error: The exception that occurred
            text: Text that failed to synthesize
            attempt: Current attempt number
            max_retries: Maximum number of retries
        """
        if attempt < max_retries:
            print(f"  Synthesis failed (attempt {attempt}/{max_retries}): {error}")
            print(f"  Retrying synthesis for: \"{text[:50]}...\"")
        else:
            print(f"  Synthesis failed after {max_retries} attempts: {error}")
            raise error