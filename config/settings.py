"""
Configuration management for the Vietnamese Audiobook TTS processor.
"""
import configparser
import os
import sys
from typing import Dict, Any


class Settings:
    """Configuration settings manager."""
    
    def __init__(self, config_file: str = 'config.ini', config_overrides: Dict[str, Any] = None):
        """
        Initialize settings from configuration file with optional runtime overrides.
        
        Args:
            config_file: Path to the configuration file
            config_overrides: Optional dictionary of configuration overrides
        """
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        self.config_overrides = config_overrides or {}
        self._load_config()
    
    def _load_config(self) -> None:
        """Load configuration from file."""
        if not os.path.exists(self.config_file):
            # Config file doesn't exist, create empty config to use fallbacks
            self.config = configparser.ConfigParser()
            return
        
        self.config.read(self.config_file)
    
    def get_gemini_api_key(self) -> str:
        """
        Get the Gemini API key from configuration or overrides.
        
        Returns:
            The API key string
            
        Raises:
            ValueError: If API key is not configured properly
        """
        # Check override first
        if 'gemini_api_key' in self.config_overrides:
            return self.config_overrides['gemini_api_key']
        
        try:
            api_key = self.config['gemini']['api_key']
            if api_key == 'YOUR_API_KEY' or not api_key:
                raise ValueError("Please replace 'YOUR_API_KEY' in config.ini with your actual Gemini API key.")
            return api_key
        except (KeyError, TypeError):
            raise ValueError("API key not found in config.ini. Please provide it in the config parameter.")
    
    def get_tts_engine(self) -> str:
        """
        Get the selected TTS engine from configuration or overrides.
        
        Returns:
            TTS engine name
        """
        # Check override first
        if 'tts_engine' in self.config_overrides:
            return self.config_overrides['tts_engine']
        
        return self.config.get('tts', 'engine', fallback='google')
    
    def get_tts_config(self) -> Dict[str, Any]:
        """
        Get TTS configuration settings for the selected engine with overrides.
        
        Returns:
            Dictionary containing TTS configuration
        """
        engine = self.get_tts_engine()
        
        # Base TTS configuration
        base_config = {
            'language_code': self.config.get('tts', 'language_code', fallback='vi-VN'),
        }
        
        # Engine-specific configuration
        if engine in ['google', 'google_tts']:
            engine_config = self._get_google_tts_config()
        elif engine == 'elevenlabs':
            engine_config = self._get_elevenlabs_config()
        elif engine == 'openai':
            engine_config = self._get_openai_tts_config()
        else:
            # Default to Google TTS config for unknown engines
            engine_config = self._get_google_tts_config()
        
        # Merge configurations
        base_config.update(engine_config)
        
        # Apply overrides if provided
        if 'tts_config' in self.config_overrides:
            base_config.update(self.config_overrides['tts_config'])
        
        return base_config
    
    def _get_google_tts_config(self) -> Dict[str, Any]:
        """Get Google TTS specific configuration."""
        return {
            'voice_name': self.config.get('google_tts', 'voice_name', fallback='vi-VN-Chirp3-HD-Algenib'),
            'speaking_rate': self.config.getfloat('google_tts', 'speaking_rate', fallback=0.99),
            'audio_encoding': self.config.get('google_tts', 'audio_encoding', fallback='LINEAR16')
        }
    
    def _get_elevenlabs_config(self) -> Dict[str, Any]:
        """Get ElevenLabs specific configuration."""
        return {
            'voice_id': self.config.get('elevenlabs', 'voice_id', fallback='pNInz6obpgDQGcFmaJgB'),
            'model_id': self.config.get('elevenlabs', 'model_id', fallback='eleven_multilingual_v2'),
            'stability': self.config.getfloat('elevenlabs', 'stability', fallback=0.5),
            'similarity_boost': self.config.getfloat('elevenlabs', 'similarity_boost', fallback=0.75),
            'api_key': self.config.get('elevenlabs', 'api_key', fallback='')
        }
    
    def _get_openai_tts_config(self) -> Dict[str, Any]:
        """Get OpenAI TTS specific configuration."""
        return {
            'voice': self.config.get('openai_tts', 'voice', fallback='alloy'),
            'model': self.config.get('openai_tts', 'model', fallback='tts-1'),
            'speed': self.config.getfloat('openai_tts', 'speed', fallback=1.0),
            'api_key': self.config.get('openai_tts', 'api_key', fallback='')
        }
    
    def get_processing_config(self) -> Dict[str, Any]:
        """
        Get processing configuration settings.
        
        Returns:
            Dictionary containing processing configuration
        """
        return {
            'min_break_duration': self.config.getint('processing', 'min_break_duration', fallback=1500),
            'max_break_duration': self.config.getint('processing', 'max_break_duration', fallback=2000),
            'max_workers': self.config.getint('processing', 'max_workers', fallback=10),
            'segment_batch_size': self.config.getint('processing', 'segment_batch_size', fallback=5),
            'retry_attempts': self.config.getint('processing', 'retry_attempts', fallback=5),
            'backoff_seconds': self.config.getint('processing', 'backoff_seconds', fallback=15)
        }


# Global settings instance
settings = Settings()