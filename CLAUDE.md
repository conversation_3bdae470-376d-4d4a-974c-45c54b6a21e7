# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Basic Usage
```bash
# Install dependencies
pip install -r requirements.txt

# Run the main application
python main.py [input_file] [tagged_file] [output_audio_file]

# Example with options
python main.py input.txt tagged.txt output.wav --force-tagging --max-workers 20 --verbose

# Preview mode (no audio generation)
python main.py input.txt tagged.txt output.wav --preview

# Validation only
python main.py input.txt tagged.txt output.wav --validate-only
```

### Authentication Setup
```bash
# Google Cloud authentication (required for TTS)
gcloud auth application-default login

# Configure Gemini API key in config.ini file
# Replace 'YOUR_API_KEY' with actual API key
```

## Architecture Overview

This is a modular Vietnamese audiobook text-to-speech processor with a layered architecture:

### Core Processing Flow
1. **Text Processing**: Raw text → Gemini AI tagging → Pause-tagged text
2. **Validation**: Input validation and preview capabilities  
3. **Audio Synthesis**: Parallel segment processing → TTS → Audio combination

### Module Structure
- **`main.py`**: Application orchestrator and entry point
- **`cli/`**: Command-line interface and argument parsing
- **`config/`**: Configuration management (config.ini loading)
- **`services/`**: External API integrations (Gemini, Google Cloud TTS, File I/O)
- **`core/`**: Business logic (TextProcessor, AudioSynthesizer, SegmentProcessor)
- **`utils/`**: Reusable utilities (text, audio, concurrency)

### Key Design Patterns
- **Dependency Injection**: Services are injected into core processors
- **Parallel Processing**: Concurrent synthesis of sentences within segments
- **Batch Processing**: Segments processed in configurable batches
- **Error Handling**: Retry logic with exponential backoff for API rate limits

### Configuration Files
- **`config.ini`**: Contains Gemini API key and TTS settings (not in repo)
- **`requirements.txt`**: Python dependencies

### Processing Parameters
- Break durations: `--min-break` and `--max-break` (milliseconds)
- Concurrency: `--max-workers` for sentence synthesis, `--segment-batch-size` for parallel segments
- Default TTS voice: `vi-VN-Chirp3-HD-Algenib` (Vietnamese Chirp 3 HD)

### Text Tag System
- `[break]`: Major breaks between segments (with silence insertion)
- `[pause]`, `[pause short]`, `[pause long]`: Minor pauses within segments
- Tags are added by Gemini AI for natural speech flow

### Development Notes
- No package.json or build system - pure Python application
- Requires Google Cloud credentials and Gemini API key setup
- Audio output formats: .wav or .mp3
- Designed for Vietnamese text processing with number/unit conversion

## Development Workflow

### Basic Commands

```bash
# Run the application
python main.py input.txt tagged.txt output.wav

# Test with different TTS engines
python main.py input.txt tagged.txt output.wav --tts-engine openai_tts

# Preview without generating audio
python main.py input.txt tagged.txt output.wav --preview

# Validate input only
python main.py input.txt tagged.txt output.wav --validate-only
```

### Testing and Quality

#### Manual Testing Checklist
- [ ] CLI functionality: `python main.py --help`
- [ ] Library API: `from api import process_text_to_audio`
- [ ] Authentication setup works
- [ ] Error handling for missing API keys
- [ ] Performance with concurrent processing
- [ ] Different TTS engines work correctly

#### Code Quality Checks
```bash
# Test all Python files compile
find . -name "*.py" -exec python -m py_compile {} \;

# Test library import
python -c "from api import process_text_to_audio; print('API import successful')"

# Test CLI
python main.py --help
```

## Library API Usage

This repository can be used as a Python library for integration with other projects.

### Direct Usage
```bash
# Clone the repository
git clone https://bitbucket.org/fonos/google-voice.git

# Add to Python path and import
import sys
sys.path.append('path/to/google-voice')
from api import process_text_to_audio
```

### Git Submodule Usage
```bash
# Add as submodule
git submodule add https://bitbucket.org/fonos/google-voice.git libs/google-voice

# Import in Python
import sys
sys.path.append('libs/google-voice')
from api import process_text_to_audio
```

### Core API Function

#### `process_text_to_audio(text, config=None, return_bytes=False)`

**Purpose**: Convert Vietnamese text to speech audio with AI-powered pause tagging.

**Parameters**:
- `text` (str): Raw Vietnamese text content to convert
- `config` (dict, optional): Configuration overrides:
  ```python
  {
      "gemini_api_key": "your_api_key",  # Required if not in config.ini
      "min_break": 1500,                 # Min break duration (ms)
      "max_break": 2000,                 # Max break duration (ms)  
      "max_workers": 10,                 # Parallel workers
      "segment_batch_size": 5,           # Segment batch size
      "tts_config": {                    # TTS overrides
          "language_code": "vi-VN",
          "voice_name": "vi-VN-Chirp3-HD-Algenib",
          "speaking_rate": 0.99
      }
  }
  ```
- `return_bytes` (bool): Return audio bytes (True) or temp file path (False)

**Returns**:
- If `return_bytes=True`: `bytes` object with WAV audio data
- If `return_bytes=False`: `str` path to temporary audio file

**Example Usage**:
```python
# For Firebase Functions / GCS upload
audio_data = process_text_to_audio(
    text=chapter_content,
    config={"gemini_api_key": os.environ["GEMINI_API_KEY"]},
    return_bytes=True
)

# Upload to Google Cloud Storage
blob = bucket.blob(f"audio/chapter_{num:02d}.wav")
blob.upload_from_string(audio_data, content_type='audio/wav')

# For local file processing
audio_path = process_text_to_audio(
    text=chapter_content,
    return_bytes=False
)
# Process local file, then clean up
os.unlink(audio_path)
```

### Integration Notes
- **Loose Coupling**: All existing CLI functionality preserved unchanged
- **Configuration Flexibility**: Supports runtime config without requiring config.ini files
- **Memory Efficient**: Can return bytes directly for cloud storage upload
- **Error Handling**: Comprehensive error messages and cleanup of temporary files